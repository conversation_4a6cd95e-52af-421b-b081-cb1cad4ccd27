<manifest xmlns:android="http://schemas.android.com/apk/res/android">

        <uses-permission android:name="android.permission.INTERNET"/>
        <uses-permission android:name="android.permission.READ_CONTACTS" />
        <uses-permission android:name="android.permission.WRITE_CONTACTS" />
        <uses-permission android:name="android.permission.READ_PHONE_STATE" />
        <uses-permission android:name="android.permission.READ_CALL_LOG" />
        <uses-permission android:name="android.permission.PROCESS_OUTGOING_CALLS" />
        <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
        <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
        <!-- For Android 12+ (API level 31+) -->
        <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
        <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>

        <!-- Background execution permissions -->
        <uses-permission android:name="android.permission.WAKE_LOCK" />
        <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
        <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

        <!-- For Android 13+ (API level 33+) -->
        <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
        <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
        <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
        <uses-permission android:name="android.permission.VIBRATE" />
        <!--           current location access-->
        <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
        <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>

    <application
        android:label="iCRM"
        android:name="${applicationName}"
        android:usesCleartextTraffic="true"
        android:requestLegacyExternalStorage="true"
        android:allowBackup="true"
        android:supportsRtl="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:icon="@mipmap/ic_launcher">

        <!-- Overlay Service -->
        <service
            android:name=".OverlayService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="phoneCall"
            android:stopWithTask="false" />

        <!-- Background Call Detection Service -->
        <service
            android:name=".CallDetectionBackgroundService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="phoneCall"
            android:stopWithTask="false" />

        <!-- Phone State Listener Service for persistent call detection -->
        <service
            android:name=".PhoneStateListenerService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="phoneCall"
            android:stopWithTask="false" />



        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />

            <!-- for notification channel -->
         <meta-data
           android:name="com.google.firebase.messaging.default_notification_channel_id"
           android:value="high_importance_channel" />


         <!-- Firebase Messaging Service -->

        <receiver android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver"
                android:exported="true">
                <intent-filter>
                    <action android:name="android.intent.action.BOOT_COMPLETED"/>
                </intent-filter>
        </receiver>


        <!--Board Cast Receiver for call action-->
       <receiver
           android:name=".AppReceiver"
           android:enabled="true"
           android:exported="true"
           android:permission="android.permission.READ_PHONE_STATE">
           <intent-filter>
               <action android:name="android.intent.action.PHONE_STATE" />
               <action android:name="android.intent.action.READ_PHONE_STATE" />
               <action android:name="android.intent.action.NEW_OUTGOING_CALL"/>
               <action android:name="android.intent.action.CALL_PHONE"/>
               <action android:name="icrm_call_action" />
           </intent-filter>
       </receiver>

       <!-- Call State Receiver for system-wide call detection -->
       <receiver
           android:name=".CallStateReceiver"
           android:enabled="true"
           android:exported="true"
           android:directBootAware="true">
           <intent-filter android:priority="1000">
               <action android:name="android.intent.action.PHONE_STATE" />
               <action android:name="android.intent.action.NEW_OUTGOING_CALL" />
           </intent-filter>
       </receiver>

       <!-- Boot Receiver to ensure call detection works after reboot -->
       <receiver
           android:name=".BootReceiver"
           android:enabled="true"
           android:exported="true"
           android:directBootAware="true">
           <intent-filter android:priority="1000">
               <action android:name="android.intent.action.BOOT_COMPLETED" />
               <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
               <action android:name="android.intent.action.PACKAGE_REPLACED" />
               <data android:scheme="package" />
           </intent-filter>
       </receiver>

       <!-- home screen widget -->

       <receiver android:name=".TaskWidgetProvider" android:exported="true">
    <intent-filter>
        <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
    </intent-filter>
    <meta-data
        android:name="android.appwidget.provider"
        android:resource="@xml/task_widget_info" />
</receiver>
<service
    android:name=".TaskWidgetService"
    android:permission="android.permission.BIND_REMOTEVIEWS" />


    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility?hl=en and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT"/>
            <data android:mimeType="text/plain"/>
        </intent>

        <intent>
            <action android:name="android.intent.action.DIAL" />
            <data android:scheme="tel" />
        </intent>
    </queries>
</manifest>
