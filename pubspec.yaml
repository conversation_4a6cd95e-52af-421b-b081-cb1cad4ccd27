name: untitled1
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.3.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  flutter_launcher_icons: ^0.13.1
  flutter_spinkit: 5.2.1
  google_fonts: 
  intl: 0.19.0
  flutter_screenutil: 5.9.3
  animated_floating_buttons: 0.0.2
  clickable_list_wheel_view: 0.2.0
  animated_splash_screen: 1.3.0
  transition: 2.0.2
  unique_simple_bar_chart: 0.0.4
  flutter_animated_dialog: 2.0.1
  awesome_dialog: ^3.2.0
  get: 4.6.6
  page_transition: 2.1.0
  loading_animation_widget: 1.2.1
  http: 
  shared_preferences: ^2.3.0
  simple_fontellico_progress_dialog: ^0.3.0
  fl_chart: ^0.68.0
  contacts_service: ^0.6.3
  permission_handler: ^11.3.1
  pull_to_refresh_flutter3: ^2.0.2
  connectivity_plus: ^6.1.0
  carousel_slider: ^5.0.0
  dropdown_button2: ^2.3.9
  badges: ^3.1.2
  # notification
  firebase_core: ^2.15.0
  firebase_auth:
  firebase_database:
  cloud_firestore: ^4.13.5
  firebase_messaging:
  flutter_local_notifications: ^17.2.3
  app_settings: ^5.1.1
  googleapis_auth: ^1.4.1
  googleapis:

  # notification end
  url_launcher: 


  # 
  #VOIP SDK
  voip24h_sdk_mobile: 
    path: ./voip24h_sdk_mobile

  
  flutter_contacts: ^1.1.9+2
  # database
  sqflite: ^2.4.1 
  
  #Call recorder
  record:
    path: ./record

  marquee: ^2.2.3

  #  multiselect drop down menu
  multi_select_flutter: ^4.1.3
  # location traking
  # geolocator: ^10.1.0
  # geocoding: ^2.1.1

  

#  app launching logo 
flutter_icons:
  image_path: "assets/images/ilogo.png"
  android: true
  ios: true




dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0
  flutter_launcher_icons: 0.13.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  fonts:
    - family: HindSiliguri
      fonts:
        - asset: assets/fonts/HindSiliguri-Regular.ttf
        # - asset: fonts/Raleway-Italic.ttf

    - family: HindSiliguriBold
      fonts:
        - asset: assets/fonts/HindSiliguri-Bold.ttf
          weight: 700

    - family: Inter18
      fonts:
        - asset: assets/fonts/Inter_18pt-Regular.ttf
          weight: 700

    - family: HindSiliguriBold
      fonts:
        - asset: assets/fonts/HindSiliguri-Bold.ttf
          weight: 700
    

    - family: HindSiliguriBold
      fonts:
        - asset: assets/fonts/HindSiliguri-Bold.ttf
          weight: 700
    
          
    

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true


  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    
  #    - assets/icons/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
