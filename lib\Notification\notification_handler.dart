// import 'package:flutter_local_notifications/flutter_local_notifications.dart';

// class NotificationHandler {
//   static FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
//       FlutterLocalNotificationsPlugin();

//   static void initialize() {
//     var initializationSettingsAndroid =
//         AndroidInitializationSettings('app_icon');
//     // var initializationSettingsIOS = IOSInitializationSettings(
//     //   onDidReceiveLocalNotification: (id, title, body, payload) async {},
//     // );
//     var initializationSettings = InitializationSettings(
//       android: initializationSettingsAndroid,
//       //iOS: initializationSettingsIOS,
//     );
//     flutterLocalNotificationsPlugin.initialize(
//       initializationSettings,
//       // onSelectNotification: (String? payload) async {
//       //   if (payload != null) {
//       //     print('Notification clicked: $payload');
//       //     // Handle notification click event
//       //   }
//     );
//   }

//   static Future<void> showNotification(
//       String title, String body, String payload) async {
//     var androidPlatformChannelSpecifics = AndroidNotificationDetails(
//       'your_channel_id', // Change this to your channel ID
//       'Your Channel Name', // Change this to your channel name
//       importance: Importance.max,
//       priority: Priority.high,
//       ticker: 'ticker',
//     );

//     var platformChannelSpecifics = NotificationDetails(
//       android: androidPlatformChannelSpecifics,
//     );
//     await flutterLocalNotificationsPlugin.show(
//       0, // Notification ID
//       title,
//       body,
//       platformChannelSpecifics,
//       payload: payload,
//     );
//   }
// }
