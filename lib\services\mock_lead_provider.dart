import '../Models/LeadListModel.dart';

class MockLeadProvider {
  static List<LeadListModel> getMockLeads() {
    return [
      LeadListModel(
        id: 1,
        name: '<PERSON>',
        phoneNumber: '**********',
        email: '<EMAIL>',
        companyName: 'ABC Corp',
        companyEmail: '<EMAIL>',
        leadPipelineId: 1,
        leadAreaId: 1,
        userId: 1,
        createdAt: '2023-01-01',
        amount: '1000',
        leadSourceId: 1,
        associates: [],
      ),
      LeadListModel(
        id: 2,
        name: '<PERSON>',
        phoneNumber: '**********',
        email: '<EMAIL>',
        companyName: 'XYZ Ltd',
        companyEmail: '<EMAIL>',
        leadPipelineId: 2,
        leadAreaId: 2,
        userId: 2,
        createdAt: '2023-01-02',
        amount: '2000',
        leadSourceId: 2,
        associates: [],
      ),
      LeadListModel(
        id: 3,
        name: '<PERSON>',
        phoneNumber: '**********',
        email: '<EMAIL>',
        companyName: 'DEF Inc',
        companyEmail: '<EMAIL>',
        leadPipelineId: 3,
        leadAreaId: 3,
        userId: 3,
        createdAt: '2023-01-03',
        amount: '3000',
        leadSourceId: 3,
        associates: [],
      ),
      // Adding leads with Bangladesh phone number format
      LeadListModel(
        id: 4,
        name: 'Rahim Ahmed',
        phoneNumber: '01645467222', // The number you tested with
        email: '<EMAIL>',
        companyName: 'Bangladesh Tech',
        companyEmail: '<EMAIL>',
        leadPipelineId: 1,
        leadAreaId: 1,
        userId: 1,
        createdAt: '2023-01-04',
        amount: '4000',
        leadSourceId: 1,
        associates: [],
      ),
      LeadListModel(
        id: 5,
        name: 'Karim Khan',
        phoneNumber: '01712345678',
        email: '<EMAIL>',
        companyName: 'Dhaka Solutions',
        companyEmail: '<EMAIL>',
        leadPipelineId: 2,
        leadAreaId: 2,
        userId: 2,
        createdAt: '2023-01-05',
        amount: '5000',
        leadSourceId: 2,
        associates: [],
      ),
    ];
  }
}
