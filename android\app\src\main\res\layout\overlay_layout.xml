<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="#FFFFFF"
    android:padding="16dp"
    android:elevation="8dp">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/imageViewIcon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@mipmap/ic_launcher"
            android:contentDescription="App Icon" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginStart="16dp">

            <TextView
                android:id="@+id/textViewName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Unknown Contact"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="#000000" />

            <TextView
                android:id="@+id/textViewPhone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="+1234567890"
                android:textSize="14sp"
                android:textColor="#666666" />

            <LinearLayout
                android:id="@+id/pipelineSection"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="4dp">
                
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Status:"
                    android:textSize="12sp"
                    android:textColor="#666666" />
                
                <TextView
                    android:id="@+id/textViewPipeline"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="N/A"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="#4285F4"
                    android:layout_marginStart="4dp" />

                <Button
                    android:id="@+id/buttonUpdatePipeline"
                    android:layout_width="wrap_content"
                    android:layout_height="24dp"
                    android:text="Update"
                    android:textSize="10sp"
                    android:textColor="#FFFFFF"
                    android:background="#4285F4"
                    android:layout_marginStart="8dp"
                    android:minWidth="0dp"
                    android:minHeight="0dp"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp" />
            </LinearLayout>
        </LinearLayout>

        <Button
            android:id="@+id/buttonClose"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="@android:drawable/ic_menu_close_clear_cancel"
            android:contentDescription="Close" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="16dp">

        <Button
            android:id="@+id/buttonCall"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Call Back"
            android:textColor="#FFFFFF"
            android:backgroundTint="#4CAF50" />

        <Button
            android:id="@+id/buttonAddLead"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="View Details"
            android:textColor="#FFFFFF"
            android:backgroundTint="#2196F3" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="8dp">

        <Button
            android:id="@+id/buttonCreateTask"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Create Task"
            android:textColor="#FFFFFF"
            android:backgroundTint="#FF9800" />

        <Button
            android:id="@+id/buttonFollowUp"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="Follow-up"
            android:textColor="#FFFFFF"
            android:backgroundTint="#9C27B0" />

    </LinearLayout>

</LinearLayout>
